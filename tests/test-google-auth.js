// Test script to verify Google OAuth configuration
const fs = require('fs');
const path = require('path');

function testGoogleAuthSetup() {
  console.log('🔍 Testing Google OAuth Configuration...\n');

  let hasErrors = false;

  // Test 1: Check if .env file exists
  console.log('1. Checking .env file...');
  const envPath = path.join(__dirname, '.env');
  if (!fs.existsSync(envPath)) {
    console.log('❌ .env file not found');
    console.log('   Please copy .env.example to .env and configure your Google OAuth credentials');
    hasErrors = true;
  } else {
    console.log('✅ .env file found');
  }

  // Test 2: Check environment variables
  require('dotenv').config();
  
  console.log('\n2. Checking environment variables...');
  
  const requiredVars = [
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET', 
    'GOOGLE_CALLBACK_URL',
    'SESSION_SECRET'
  ];

  requiredVars.forEach(varName => {
    if (!process.env[varName] || process.env[varName].includes('your_') || process.env[varName].includes('_here')) {
      console.log(`❌ ${varName} not properly configured`);
      hasErrors = true;
    } else {
      console.log(`✅ ${varName} configured`);
    }
  });

  // Test 3: Check database schema
  console.log('\n3. Checking database schema...');
  try {
    const dbPath = path.join(__dirname, 'database.json');
    if (fs.existsSync(dbPath)) {
      const data = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
      
      if (data.users && Array.isArray(data.users)) {
        console.log('✅ Database structure is valid');
        
        // Check if users have Google auth fields
        if (data.users.length > 0) {
          const sampleUser = data.users[0];
          if (sampleUser.hasOwnProperty('google_id') && sampleUser.hasOwnProperty('profile_picture')) {
            console.log('✅ Database schema supports Google authentication');
          } else {
            console.log('❌ Database schema missing Google authentication fields');
            console.log('   The database will be automatically updated when a Google user signs in');
          }
        }
      } else {
        console.log('❌ Invalid database structure');
        hasErrors = true;
      }
    } else {
      console.log('✅ Database will be created automatically');
    }
  } catch (error) {
    console.log('❌ Error reading database:', error.message);
    hasErrors = true;
  }

  // Test 4: Check passport configuration
  console.log('\n4. Checking passport configuration...');
  try {
    const passportPath = path.join(__dirname, 'backend/config/passport.js');
    if (fs.existsSync(passportPath)) {
      console.log('✅ Passport configuration file exists');
    } else {
      console.log('❌ Passport configuration file missing');
      hasErrors = true;
    }
  } catch (error) {
    console.log('❌ Error checking passport configuration:', error.message);
    hasErrors = true;
  }

  // Test 5: Check authentication routes
  console.log('\n5. Checking authentication routes...');
  try {
    const authRoutesPath = path.join(__dirname, 'backend/routes/auth.js');
    if (fs.existsSync(authRoutesPath)) {
      console.log('✅ Authentication routes file exists');
    } else {
      console.log('❌ Authentication routes file missing');
      hasErrors = true;
    }
  } catch (error) {
    console.log('❌ Error checking authentication routes:', error.message);
    hasErrors = true;
  }

  // Summary
  console.log('\n' + '='.repeat(50));
  if (hasErrors) {
    console.log('❌ Google OAuth setup has issues that need to be resolved');
    console.log('\nNext steps:');
    console.log('1. Follow the GOOGLE_OAUTH_SETUP.md guide');
    console.log('2. Create and configure your .env file');
    console.log('3. Set up Google OAuth credentials in Google Cloud Console');
    console.log('4. Run this test again to verify the setup');
  } else {
    console.log('✅ Google OAuth setup looks good!');
    console.log('\nTo test the authentication flow:');
    console.log('1. Start the server: npm start');
    console.log('2. Go to http://localhost:3000/login.html');
    console.log('3. Click "Login with Google"');
    console.log('4. Complete the OAuth flow');
  }
  console.log('='.repeat(50));

  return !hasErrors;
}

// Run the test if this script is executed directly
if (require.main === module) {
  testGoogleAuthSetup();
}

module.exports = { testGoogleAuthSetup };

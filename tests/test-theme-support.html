<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mermaid Theme Support</title>
    <script src="public/js/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ccc;
            background-color: white;
            border-radius: 8px;
        }
        .theme-selector {
            margin: 10px 0;
        }
        .theme-selector select {
            padding: 5px;
            margin-left: 10px;
        }
        .diagram-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 300px;
        }
    </style>
</head>
<body>
    <h1>🎨 Mermaid Theme Support Test</h1>
    <p>This test verifies that different Mermaid themes can be applied dynamically.</p>

    <div class="test-container">
        <h2>Theme Selector</h2>
        <div class="theme-selector">
            <label for="theme-select">Choose Theme:</label>
            <select id="theme-select">
                <option value="default">Default</option>
                <option value="dark">Dark</option>
                <option value="forest">Forest</option>
                <option value="base">Base</option>
                <option value="neutral">Neutral</option>
            </select>
            <button id="apply-theme">Apply Theme</button>
        </div>
        
        <div class="diagram-container">
            <div id="diagram" class="mermaid">
graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Process A]
    B -->|No| D[Process B]
    C --> E[End]
    D --> E
    E --> F[🎯 Success]
            </div>
        </div>
    </div>

    <script>
        let currentTheme = 'default';
        
        // Initialize Mermaid with default theme
        function initializeMermaid(theme = 'default') {
            mermaid.initialize({
                startOnLoad: false,
                securityLevel: 'loose',
                theme: theme,
                logLevel: 'fatal',
                fontFamily: 'monospace',
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true,
                    curve: 'linear'
                }
            });
        }
        
        // Function to re-render diagram with new theme
        function applyTheme(newTheme) {
            console.log('Applying theme:', newTheme);
            currentTheme = newTheme;
            
            // Re-initialize mermaid with new theme
            initializeMermaid(newTheme);
            
            // Clear and re-render the diagram
            const diagramElement = document.getElementById('diagram');
            const originalContent = diagramElement.textContent;
            
            // Clear the diagram
            diagramElement.innerHTML = '';
            diagramElement.textContent = originalContent;
            
            // Re-render with new theme
            setTimeout(() => {
                try {
                    mermaid.init(undefined, diagramElement);
                    console.log('✅ Theme applied successfully:', newTheme);
                } catch (error) {
                    console.error('❌ Error applying theme:', error);
                }
            }, 100);
        }
        
        // Initialize with default theme
        initializeMermaid();
        
        document.addEventListener('DOMContentLoaded', function() {
            // Initial render
            setTimeout(() => {
                mermaid.init(undefined, document.getElementById('diagram'));
            }, 100);
            
            // Theme selector event listener
            document.getElementById('apply-theme').addEventListener('click', function() {
                const selectedTheme = document.getElementById('theme-select').value;
                applyTheme(selectedTheme);
            });
            
            // Also apply theme when dropdown changes
            document.getElementById('theme-select').addEventListener('change', function() {
                applyTheme(this.value);
            });
        });
        
        console.log('🚀 Theme test loaded. Use the dropdown to test different themes.');
    </script>
</body>
</html>

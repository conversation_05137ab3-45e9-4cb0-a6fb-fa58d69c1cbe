// Simple test script to verify password reset functionality
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testPasswordReset() {
  console.log('Testing password reset functionality...\n');

  try {
    // Test 1: Request password reset for existing user
    console.log('1. Testing password reset request for existing user...');
    const resetResponse = await fetch(`${BASE_URL}/api/users/forgot-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email: '<EMAIL>' })
    });

    const resetData = await resetResponse.json();
    console.log('Response:', resetData);

    if (resetResponse.ok) {
      console.log('✅ Password reset request successful\n');
    } else {
      console.log('❌ Password reset request failed\n');
    }

    // Test 2: Request password reset for non-existing user
    console.log('2. Testing password reset request for non-existing user...');
    const resetResponse2 = await fetch(`${BASE_URL}/api/users/forgot-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email: '<EMAIL>' })
    });

    const resetData2 = await resetResponse2.json();
    console.log('Response:', resetData2);

    if (resetResponse2.ok) {
      console.log('✅ Password reset request handled correctly (security response)\n');
    } else {
      console.log('❌ Password reset request failed unexpectedly\n');
    }

    // Test 3: Test registration with welcome email
    console.log('3. Testing user registration with welcome email...');
    const registerResponse = await fetch(`${BASE_URL}/api/users/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        username: 'testuser' + Date.now(), 
        email: 'test' + Date.now() + '@example.com',
        password: 'testpassword123'
      })
    });

    const registerData = await registerResponse.json();
    console.log('Response:', registerData);

    if (registerResponse.ok) {
      console.log('✅ User registration successful (welcome email should be sent)\n');
    } else {
      console.log('❌ User registration failed\n');
    }

  } catch (error) {
    console.error('Test error:', error);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testPasswordReset();
}

module.exports = { testPasswordReset };

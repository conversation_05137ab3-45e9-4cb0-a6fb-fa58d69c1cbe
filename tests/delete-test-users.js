#!/usr/bin/env node

/**
 * <PERSON>ript to delete test users from the database
 * Deletes users whose username starts with "testuser"
 * Also deletes their associated charts and sessions
 */

const db = require('./backend/db');

console.log('🧹 Test User Cleanup Script');
console.log('============================\n');

async function deleteTestUsers() {
  try {
    // First, let's see what test users exist
    console.log('1. Finding test users...');
    
    const testUsers = await new Promise((resolve, reject) => {
      db.all(
        "SELECT id, username, email, auth_type, created_at FROM users WHERE username LIKE 'testuser%'",
        (err, users) => {
          if (err) reject(err);
          else resolve(users);
        }
      );
    });

    if (testUsers.length === 0) {
      console.log('✅ No test users found (username starting with "testuser")');
      return;
    }

    console.log(`📋 Found ${testUsers.length} test user(s):`);
    testUsers.forEach(user => {
      console.log(`   - ${user.username} (${user.email}) - Created: ${user.created_at}`);
    });

    // Get test user IDs for chart deletion
    const testUserIds = testUsers.map(user => user.id);
    
    // Check for associated charts
    console.log('\n2. Checking for associated charts...');
    
    const testCharts = await new Promise((resolve, reject) => {
      const placeholders = testUserIds.map(() => '?').join(',');
      db.all(
        `SELECT id, title, user_id FROM charts WHERE user_id IN (${placeholders})`,
        testUserIds,
        (err, charts) => {
          if (err) reject(err);
          else resolve(charts);
        }
      );
    });

    if (testCharts.length > 0) {
      console.log(`📊 Found ${testCharts.length} chart(s) belonging to test users:`);
      testCharts.forEach(chart => {
        const user = testUsers.find(u => u.id === chart.user_id);
        console.log(`   - "${chart.title}" (owned by ${user.username})`);
      });
    } else {
      console.log('✅ No charts found for test users');
    }

    // Confirm deletion
    console.log('\n⚠️  WARNING: This will permanently delete:');
    console.log(`   - ${testUsers.length} test user(s)`);
    console.log(`   - ${testCharts.length} associated chart(s)`);
    console.log('   - Any associated sessions');
    
    // In a real interactive environment, you'd want to prompt for confirmation
    // For now, we'll add a safety check
    const confirmDelete = process.argv.includes('--confirm');
    
    if (!confirmDelete) {
      console.log('\n❌ Deletion cancelled for safety.');
      console.log('To actually delete the test users, run:');
      console.log('   node delete-test-users.js --confirm');
      return;
    }

    console.log('\n3. Deleting test data...');

    // Delete charts first (due to foreign key constraints)
    if (testCharts.length > 0) {
      console.log('   Deleting charts...');
      const chartIds = testCharts.map(chart => chart.id);
      const chartPlaceholders = chartIds.map(() => '?').join(',');
      
      await new Promise((resolve, reject) => {
        db.run(
          `DELETE FROM charts WHERE id IN (${chartPlaceholders})`,
          chartIds,
          function(err) {
            if (err) reject(err);
            else {
              console.log(`   ✅ Deleted ${this.changes} chart(s)`);
              resolve();
            }
          }
        );
      });
    }

    // Delete sessions for test users (if any)
    console.log('   Cleaning up sessions...');
    await new Promise((resolve, reject) => {
      db.run(
        "DELETE FROM sessions WHERE sess LIKE '%testuser%'",
        function(err) {
          if (err) reject(err);
          else {
            console.log(`   ✅ Cleaned up ${this.changes} session(s)`);
            resolve();
          }
        }
      );
    });

    // Finally, delete the test users
    console.log('   Deleting test users...');
    await new Promise((resolve, reject) => {
      db.run(
        "DELETE FROM users WHERE username LIKE 'testuser%'",
        function(err) {
          if (err) reject(err);
          else {
            console.log(`   ✅ Deleted ${this.changes} test user(s)`);
            resolve();
          }
        }
      );
    });

    console.log('\n🎉 Test user cleanup completed successfully!');
    
    // Verify cleanup
    console.log('\n4. Verifying cleanup...');
    const remainingTestUsers = await new Promise((resolve, reject) => {
      db.all(
        "SELECT COUNT(*) as count FROM users WHERE username LIKE 'testuser%'",
        (err, result) => {
          if (err) reject(err);
          else resolve(result[0].count);
        }
      );
    });

    if (remainingTestUsers === 0) {
      console.log('✅ Verification passed: No test users remaining');
    } else {
      console.log(`⚠️  Warning: ${remainingTestUsers} test users still found`);
    }

    // Show final user count
    const totalUsers = await new Promise((resolve, reject) => {
      db.all(
        "SELECT COUNT(*) as count FROM users",
        (err, result) => {
          if (err) reject(err);
          else resolve(result[0].count);
        }
      );
    });

    console.log(`📊 Total users remaining: ${totalUsers}`);

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    process.exit(1);
  }
}

// Run the cleanup
deleteTestUsers().then(() => {
  console.log('\n✨ Cleanup script completed');
  process.exit(0);
}).catch((error) => {
  console.error('\n💥 Cleanup script failed:', error);
  process.exit(1);
});

// Test script to verify the specific "UnknownDiagramError" is fixed
// This tests the exact error case from the user's report

console.log('🔍 Testing specific UnknownDiagramError fix...\n');

// Simulate the sanitizeMermaidContent function from dashboard.html
function sanitizeMermaidContent(content) {
    if (!content) return {
        content: content,
        hasReplacements: false,
        originalContent: content
    };

    // Map of specific problematic Unicode characters to safe alternatives
    const unicodeReplacements = {
        '➕': '+',     // ➕ Heavy Plus Sign
        '➖': '-',     // ➖ Heavy Minus Sign
        '✓': 'check', // ✓ Check Mark
        '✗': 'x',     // ✗ Ballot X
        '⚠': 'warn',  // ⚠ Warning Sign
        '⭐': 'star',  // ⭐ White Medium Star
        '🔥': 'fire',   // 🔥 Fire
        '💡': 'idea',   // 💡 Electric Light Bulb
        '📊': 'chart',  // 📊 Bar Chart
        '📈': 'up',     // 📈 Chart Increasing
        '📉': 'down',   // 📉 Chart Decreasing
        '🎯': 'target', // 🎯 Direct Hit
        '⚡': 'fast',  // ⚡ High Voltage
        '🔒': 'lock',   // 🔒 Lock
        '🔓': 'unlock', // 🔓 Open Lock
        '❌': 'x',     // ❌ Cross Mark
        '⭕': 'o',     // ⭕ Heavy Large Circle
        '🟢': 'green',  // 🟢 Green Circle
        '🔴': 'red',    // 🔴 Red Circle
        '🟡': 'yellow', // 🟡 Yellow Circle
        '🔵': 'blue',   // 🔵 Blue Circle
        '⚫': 'black', // ⚫ Medium Black Circle
        '⚪': 'white'  // ⚪ Medium White Circle
    };

    let sanitized = content;
    let hasReplacements = false;

    // Replace only the specific known problematic Unicode characters
    for (const [unicode, replacement] of Object.entries(unicodeReplacements)) {
        if (sanitized.includes(unicode)) {
            sanitized = sanitized.split(unicode).join(replacement);
            hasReplacements = true;
        }
    }

    // Only replace specific invisible/control characters that break parsing
    const invisibleCharReplacements = [
        [/[\u2000-\u200F]/g, ' '],  // Various spaces and invisible chars
        [/[\u2028-\u2029]/g, ' '],  // Line/paragraph separators
        [/[\uFEFF]/g, ''],          // Byte order mark (remove completely)
        [/[\u00A0]/g, ' ']          // Non-breaking space
    ];

    for (const [regex, replacement] of invisibleCharReplacements) {
        const matches = sanitized.match(regex);
        if (matches && matches.length > 0) {
            sanitized = sanitized.replace(regex, replacement);
            hasReplacements = true;
        }
    }

    return {
        content: sanitized,
        hasReplacements: hasReplacements,
        originalContent: content
    };
}

// Test the exact problematic content that caused the original error
const problematicContent = `graph TD
    A[Start] --> B{Decision}
    B -->|Yes| f1[➕]
    B -->|No| f2[➖]
    f1 --- f2`;

console.log('📋 Original problematic content:');
console.log(problematicContent);
console.log('\n🔧 Running sanitization...');

const result = sanitizeMermaidContent(problematicContent);

console.log('📋 Sanitized content:');
console.log(result.content);
console.log('\n📊 Sanitization results:');
console.log('- Has replacements:', result.hasReplacements);
console.log('- Original length:', result.originalContent.length);
console.log('- Sanitized length:', result.content.length);

// Check if the sanitized content would cause the UnknownDiagramError
console.log('\n🧪 Testing for UnknownDiagramError patterns...');

// The original error was caused by content like "_____ __"
// Let's check if our sanitized content has this pattern
const hasUnderscorePattern = /_{3,}/.test(result.content);
const hasQuestionMarkPattern = /\?{3,}/.test(result.content);
const startsWithValidMermaid = /^(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|erDiagram|journey|gitgraph|pie|gantt|mindmap|timeline|quadrantChart|requirement|c4Context)/i.test(result.content.trim());

console.log('- Contains underscore pattern (_____):', hasUnderscorePattern);
console.log('- Contains question mark pattern (???):', hasQuestionMarkPattern);
console.log('- Starts with valid Mermaid keyword:', startsWithValidMermaid);

// Final assessment
if (!hasUnderscorePattern && !hasQuestionMarkPattern && startsWithValidMermaid) {
    console.log('\n✅ SUCCESS: The sanitized content should NOT cause UnknownDiagramError');
    console.log('   - No problematic underscore/question mark patterns');
    console.log('   - Starts with valid Mermaid diagram type');
    console.log('   - Unicode characters properly replaced');
} else {
    console.log('\n❌ POTENTIAL ISSUE: The sanitized content might still cause problems');
    if (hasUnderscorePattern) console.log('   - Still contains underscore patterns');
    if (hasQuestionMarkPattern) console.log('   - Still contains question mark patterns');
    if (!startsWithValidMermaid) console.log('   - Does not start with valid Mermaid keyword');
}

console.log('\n🎯 Expected behavior:');
console.log('   - Mermaid should be able to parse this content');
console.log('   - No "UnknownDiagramError" should occur');
console.log('   - Diagram should render with "+" and "-" instead of emoji');

// Export for Node.js if running in Node
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { sanitizeMermaidContent, problematicContent, result };
}

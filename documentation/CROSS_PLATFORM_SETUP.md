# Cross-Platform Development Setup

This guide helps you set up the mermantic project on both Windows and Linux environments.

## The Problem

The `better-sqlite3` package contains native binaries that are compiled for specific platforms. When you switch between Windows and Linux (or copy files between them), you may encounter:

- `Error: invalid ELF header`
- `Error: wrong architecture`
- `MODULE_NOT_FOUND` errors

## Quick Fix

When switching platforms, run:

```bash
# Option 1: Rebuild native modules
npm run rebuild

# Option 2: Clean install (recommended)
npm run clean-install

# Option 3: Manual cleanup
rm -rf node_modules package-lock.json
npm install
```

## Platform-Specific Setup

### Windows Setup

```bash
# Clone the repository
git clone https://github.com/chrlzs/mermantic.git
cd mermantic

# Install dependencies
npm run setup:windows

# Create environment file
cp .env.example .env
# Edit .env with your Google OAuth credentials

# Start the server
npm start
```

### Linux Setup

```bash
# Clone the repository
git clone https://github.com/chrlzs/mermantic.git
cd mermantic

# Install dependencies
npm run setup:linux

# Create environment file
cp .env.example .env
# Edit .env with your Google OAuth credentials

# Start the server
npm start
```

### WSL (Windows Subsystem for Linux)

If you're using WSL, treat it as a Linux environment:

```bash
# Inside WSL
npm run setup:linux
```

## Automated Platform Detection

The project includes automatic platform detection:

```bash
# Check if your platform is properly set up
npm run check-platform
```

This will:
- ✅ Detect your platform and architecture
- ✅ Test if better-sqlite3 is working
- ✅ Provide specific fix instructions if needed

## Development Workflow

### Switching Between Platforms

When you switch from Windows to Linux (or vice versa):

1. **Pull latest changes:**
   ```bash
   git pull
   ```

2. **Rebuild native modules:**
   ```bash
   npm run rebuild
   ```

3. **Test the setup:**
   ```bash
   npm run check-platform
   ```

4. **Start development:**
   ```bash
   npm run dev
   ```

### Using Docker (Alternative)

If you want to avoid platform issues entirely, you can use Docker:

```dockerfile
# Dockerfile (create this file)
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .
EXPOSE 3000

CMD ["npm", "start"]
```

```bash
# Build and run with Docker
docker build -t mermantic .
docker run -p 3000:3000 mermantic
```

## Troubleshooting

### Error: "invalid ELF header"

This means you have Linux binaries on Windows (or vice versa).

**Fix:**
```bash
npm run clean-install
```

### Error: "wrong architecture"

This means you have binaries for a different CPU architecture.

**Fix:**
```bash
npm rebuild better-sqlite3
```

### Error: "MODULE_NOT_FOUND"

This usually means the native module wasn't compiled properly.

**Fix:**
```bash
rm -rf node_modules/better-sqlite3
npm install better-sqlite3
```

### Still Having Issues?

If you continue having problems with `better-sqlite3`, you can use the fallback JSON database:

1. **Rename the current db.js:**
   ```bash
   mv backend/db.js backend/db-sqlite.js
   ```

2. **Use the alternative implementation:**
   ```bash
   mv backend/db-alternative.js backend/db.js
   ```

3. **Restart the server:**
   ```bash
   npm start
   ```

## Best Practices

1. **Always rebuild when switching platforms**
2. **Don't commit `node_modules` or `package-lock.json` if working across platforms**
3. **Use the platform check script before starting development**
4. **Consider using Docker for consistent environments**

## Environment-Specific Files

Add these to your `.gitignore` if working across platforms:

```gitignore
# Platform-specific files
.platform-info.json
node_modules/
package-lock.json

# Database files (if using different paths per platform)
database-windows.sqlite
database-linux.sqlite
```

## Scripts Reference

- `npm run rebuild` - Rebuild native modules
- `npm run clean-install` - Clean install all dependencies
- `npm run check-platform` - Check platform compatibility
- `npm run setup:windows` - Windows-specific setup
- `npm run setup:linux` - Linux-specific setup

## Getting Help

If you're still experiencing issues:

1. Run `npm run check-platform` and share the output
2. Include your platform info (Windows/Linux, Node.js version)
3. Share the exact error message you're seeing

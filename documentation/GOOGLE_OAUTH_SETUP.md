# Google OAuth Setup Guide

This guide will help you set up Google OAuth authentication for the mermantic application.

## Prerequisites

1. A Google account
2. Access to the Google Cloud Console

## Step 1: Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" at the top of the page
3. Click "New Project"
4. Enter a project name (e.g., "mermantic-oauth")
5. Click "Create"

## Step 2: Enable the Google+ API

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Google+ API"
3. Click on it and click "Enable"

## Step 3: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. If prompted, configure the OAuth consent screen:
   - Choose "External" for user type
   - Fill in the required fields:
     - App name: "mermantic"
     - User support email: your email
     - Developer contact information: your email
   - Click "Save and Continue"
   - Skip the "Scopes" step for now
   - Add test users if needed (your email address)
   - Click "Save and Continue"

4. Back to creating OAuth client ID:
   - Choose "Web application"
   - Name: "mermantic Web Client"
   - Authorized JavaScript origins:
     - `http://localhost:3000`
     - Add your production domain when ready
   - Authorized redirect URIs:
     - `http://localhost:3000/api/auth/google/callback`
     - Add your production callback URL when ready
   - Click "Create"

5. Copy the Client ID and Client Secret

## Step 4: Configure Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file and add your Google OAuth credentials:
   ```
   GOOGLE_CLIENT_ID=your_actual_client_id_here
   GOOGLE_CLIENT_SECRET=your_actual_client_secret_here
   GOOGLE_CALLBACK_URL=http://localhost:3000/api/auth/google/callback
   ```

3. Also set a secure session secret:
   ```
   SESSION_SECRET=your_random_session_secret_here
   ```

## Step 5: Test the Setup

1. Start the server:
   ```bash
   npm start
   ```

2. Go to `http://localhost:3000/login.html`
3. Click "Login with Google"
4. You should be redirected to Google's OAuth consent screen
5. After granting permission, you should be redirected back to the dashboard

## Troubleshooting

### Common Issues:

1. **"Error 400: redirect_uri_mismatch"**
   - Make sure the callback URL in your `.env` file exactly matches the one configured in Google Cloud Console
   - Check for trailing slashes or typos

2. **"Error 403: access_denied"**
   - Make sure you've added your email as a test user in the OAuth consent screen
   - Check that the Google+ API is enabled

3. **"Client ID not set" error in server logs**
   - Make sure your `.env` file is in the root directory
   - Restart the server after creating/modifying the `.env` file

4. **Database errors**
   - The application will automatically create the necessary database structure
   - Check that the `database.json` file has the correct schema

### Debug Mode

To see detailed authentication logs, check the server console output. The application logs all authentication steps for debugging.

## Production Deployment

When deploying to production:

1. Update the OAuth consent screen to "In production"
2. Add your production domain to authorized origins
3. Add your production callback URL to authorized redirect URIs
4. Update the `GOOGLE_CALLBACK_URL` in your production environment variables
5. Use a secure session secret in production

## Security Notes

- Never commit your `.env` file to version control
- Use HTTPS in production
- Set secure session cookies in production
- Regularly rotate your OAuth client secret

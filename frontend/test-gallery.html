<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gallery Test - mermantic</title>
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/css/gallery.css">
  <link rel="stylesheet" href="/nui/nui.css">
  
  <style>
    body {
      background: #0a0f1c;
      color: #e2e8f0;
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 20px;
    }
    
    .test-container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .test-status {
      background: #1a2332;
      border: 1px solid #2d3748;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
    }
    
    .test-status h2 {
      color: #00d4aa;
      margin-top: 0;
    }
    
    .status-item {
      margin: 10px 0;
      padding: 10px;
      border-radius: 4px;
    }
    
    .status-success {
      background: rgba(0, 255, 170, 0.1);
      border-left: 4px solid #00ffaa;
    }
    
    .status-error {
      background: rgba(255, 107, 107, 0.1);
      border-left: 4px solid #ff6b6b;
    }
    
    .status-info {
      background: rgba(0, 212, 170, 0.1);
      border-left: 4px solid #00d4aa;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <div class="test-status">
      <h2>Gallery Test Status</h2>
      <div id="test-results">
        <div class="status-item status-info">
          <strong>Testing Gallery Functionality...</strong>
        </div>
      </div>
    </div>

    <!-- Gallery Header -->
    <section class="gallery-header">
      <h2>Mermaid Chart Gallery Test</h2>
      <p>Testing the gallery functionality with predefined examples.</p>
    </section>

    <!-- Search and Filter Controls -->
    <section class="gallery-controls">
      <div class="search-container">
        <input type="text" id="gallery-search" placeholder="Search chart types..." class="search-input">
        <button id="clear-search" class="nui-button secondary">Clear</button>
      </div>
      
      <div class="filter-container">
        <label for="category-filter">Filter by category:</label>
        <select id="category-filter" class="category-select">
          <option value="all">All Categories</option>
          <option value="flowchart">Flowcharts</option>
          <option value="sequence">Sequence Diagrams</option>
          <option value="class">Class Diagrams</option>
          <option value="state">State Diagrams</option>
          <option value="gantt">Gantt Charts</option>
          <option value="pie">Pie Charts</option>
          <option value="journey">User Journey</option>
          <option value="git">Git Graphs</option>
          <option value="er">Entity Relationship</option>
          <option value="mindmap">Mind Maps</option>
          <option value="timeline">Timeline</option>
        </select>
      </div>
    </section>

    <!-- Gallery Grid -->
    <section class="gallery-grid" id="gallery-grid">
      <!-- Chart examples will be loaded here dynamically -->
    </section>

    <!-- No Results State -->
    <div id="no-results" class="no-results" style="display: none;">
      <p>No charts found matching your search criteria.</p>
      <button id="reset-filters" class="nui-button primary">Reset Filters</button>
    </div>
  </div>

  <!-- Chart Preview Modal -->
  <div id="chart-modal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="modal-title">Chart Preview</h3>
        <button id="close-modal" class="close-button">&times;</button>
      </div>
      
      <div class="modal-body">
        <div class="chart-preview-container">
          <div class="zoom-controls">
            <button class="zoom-btn" id="zoom-out" title="Zoom Out">−</button>
            <button class="zoom-btn" id="zoom-reset" title="Reset Zoom">⌂</button>
            <button class="zoom-btn" id="zoom-in" title="Zoom In">+</button>
            <button class="zoom-btn" id="fullscreen-toggle" title="Toggle Fullscreen">⛶</button>
          </div>
          <div id="modal-chart-preview" class="chart-preview chart-zoomable"></div>
        </div>
        
        <div class="chart-code-container">
          <div class="code-header">
            <h4>Mermaid Syntax</h4>
            <div class="code-actions">
              <button id="copy-code" class="nui-button secondary">📋 Copy Code</button>
              <button id="use-template" class="nui-button primary">🎨 Use as Template</button>
            </div>
          </div>
          <pre id="modal-chart-code" class="chart-code"></pre>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="/js/mermaid.min.js"></script>
  <script src="/nui/nui.js"></script>
  <script src="/js/gallery.js"></script>
  <script>
    // Test the gallery functionality
    document.addEventListener('DOMContentLoaded', () => {
      const testResults = document.getElementById('test-results');
      
      function addTestResult(message, type = 'info') {
        const div = document.createElement('div');
        div.className = `status-item status-${type}`;
        div.innerHTML = `<strong>${message}</strong>`;
        testResults.appendChild(div);
      }
      
      // Test Mermaid loading
      if (typeof mermaid !== 'undefined') {
        addTestResult('✅ Mermaid library loaded successfully', 'success');
      } else {
        addTestResult('❌ Mermaid library failed to load', 'error');
      }
      
      // Test GalleryManager loading
      if (typeof GalleryManager !== 'undefined') {
        addTestResult('✅ GalleryManager class loaded successfully', 'success');
        
        // Initialize gallery
        try {
          const gallery = new GalleryManager();
          gallery.init();
          addTestResult('✅ Gallery initialized successfully', 'success');
          
          // Check if charts were loaded
          setTimeout(() => {
            const galleryGrid = document.getElementById('gallery-grid');
            const chartCards = galleryGrid.querySelectorAll('.chart-card');
            
            if (chartCards.length > 0) {
              addTestResult(`✅ ${chartCards.length} chart examples loaded successfully`, 'success');
            } else {
              addTestResult('❌ No chart examples were loaded', 'error');
            }
          }, 1000);
          
        } catch (error) {
          addTestResult(`❌ Gallery initialization failed: ${error.message}`, 'error');
        }
      } else {
        addTestResult('❌ GalleryManager class failed to load', 'error');
      }
    });
  </script>
</body>
</html>

/* Gallery-specific styles for mermantic - Dark Underwater Theme */

/* Gallery Header */
.gallery-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem 0;
}

.gallery-header h2 {
  color: var(--text-glow);
  margin-bottom: 1rem;
  font-size: 2.5rem;
  text-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
}

.gallery-header p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Gallery Controls */
.gallery-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
}

.search-container {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex: 1;
  min-width: 250px;
}

.search-input {
  flex: 1;
  padding: 0.75rem 1rem;
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-color);
  font-size: 1rem;
  transition: var(--transition-normal);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 212, 170, 0.2);
}

.filter-container {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.filter-container label {
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.category-select {
  padding: 0.75rem 1rem;
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-color);
  font-size: 1rem;
  min-width: 180px;
  transition: var(--transition-normal);
}

.category-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 212, 170, 0.2);
}

/* Gallery Grid */
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.chart-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* Smooth easing for all properties */
  cursor: pointer;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.chart-card:hover {
  border-color: var(--border-glow);
  box-shadow: var(--box-shadow-glow);
  transform: translateY(-2px);
}

.chart-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
}

.chart-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.chart-category {
  background: var(--primary-color);
  color: var(--background-color);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chart-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 1rem;
  line-height: 1.5;
  position: relative;
  z-index: 2;
}

.chart-preview-mini {
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
  overflow: hidden;
}

/* Zoom controls for mini previews */
.chart-preview-mini .zoom-controls {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10;
}

.chart-preview-mini:hover .zoom-controls {
  opacity: 1;
}

.zoom-btn {
  background: rgba(0, 0, 0, 0.7);
  border: none;
  color: white;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  transition: background 0.2s ease;
}

.zoom-btn:hover {
  background: rgba(0, 0, 0, 0.9);
}

.zoom-btn:active {
  transform: scale(0.95);
}

.chart-preview-mini .mermaid {
  max-width: 100%;
  max-height: 180px;
}

.chart-actions {
  display: flex;
  gap: 0.5rem;
  position: relative;
  z-index: 2;
}

.chart-actions .nui-button {
  flex: 1;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

/* Loading and Empty States */
.loading-state {
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-results {
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.no-results p {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-content {
  background: var(--surface-elevated);
  border: 1px solid var(--border-glow);
  border-radius: var(--border-radius-large);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  overflow: hidden;
  box-shadow: var(--box-shadow-intense);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--background-secondary);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-glow);
  font-size: 1.5rem;
}

.close-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition-normal);
}

.close-button:hover {
  background: var(--danger-color);
  color: white;
}

.modal-body {
  padding: 1.5rem;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

.chart-preview-container {
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin-bottom: 1.5rem;
  text-align: center;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: auto;
}

/* Modal zoom controls */
.chart-preview-container .zoom-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  z-index: 10;
  background: rgba(0, 0, 0, 0.8);
  padding: 0.5rem;
  border-radius: var(--border-radius);
  backdrop-filter: blur(5px);
}

.chart-preview-container .zoom-controls .zoom-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.chart-preview-container .zoom-controls .zoom-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* Zoomable chart container */
.chart-zoomable {
  transform-origin: center center;
  transition: transform 0.3s ease;
  cursor: grab;
}

.chart-zoomable:active {
  cursor: grabbing;
}

.chart-zoomable.zoomed {
  cursor: move;
}

.chart-code-container {
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--background-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.code-header h4 {
  margin: 0;
  color: var(--text-color);
  font-size: 1.1rem;
}

.code-actions {
  display: flex;
  gap: 0.5rem;
}

.code-actions .nui-button {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.chart-code {
  background: var(--background-color);
  color: var(--text-color);
  padding: 1.5rem;
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Toast Notifications */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  z-index: 10000;
  font-weight: 500;
  animation: slideIn 0.3s ease;
}

.toast-success {
  background: var(--success-color);
  color: white;
}

.toast-error {
  background: var(--danger-color);
  color: white;
}

.toast-info {
  background: var(--primary-color);
  color: white;
}

/* Enhanced Chart Card Animations */
.chart-card {
  opacity: 1; /* Always visible */
  transform: translateY(0); /* No initial transform */
  /* Transition is already defined in the main .chart-card rule above */
}

/* Staggered fade-in animation on page load */
.chart-card:nth-child(1) { animation: fadeInUp 0.5s ease 0.1s both; }
.chart-card:nth-child(2) { animation: fadeInUp 0.5s ease 0.2s both; }
.chart-card:nth-child(3) { animation: fadeInUp 0.5s ease 0.3s both; }
.chart-card:nth-child(4) { animation: fadeInUp 0.5s ease 0.4s both; }
.chart-card:nth-child(5) { animation: fadeInUp 0.5s ease 0.5s both; }
.chart-card:nth-child(6) { animation: fadeInUp 0.5s ease 0.6s both; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Button Hover Effects */
.chart-actions .nui-button {
  position: relative;
  overflow: hidden;
}

.chart-actions .nui-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.chart-actions .nui-button:hover::after {
  width: 300px;
  height: 300px;
}

/* Fullscreen Modal */
.modal-overlay.fullscreen {
  background: rgba(0, 0, 0, 0.95);
}

.modal-content.fullscreen {
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  border-radius: 0;
  display: flex;
  flex-direction: column;
}

.modal-content.fullscreen .modal-body {
  flex: 1;
  max-height: none;
  overflow: hidden;
  display: flex;
  flex-direction: row; /* Side-by-side layout */
  gap: 1rem;
  padding: 1rem;
}

.modal-content.fullscreen .chart-preview-container {
  flex: 2; /* Take up 2/3 of the space */
  margin-bottom: 0;
  min-height: auto;
  border-radius: var(--border-radius);
  margin-right: 0;
}

.modal-content.fullscreen .chart-code-container {
  flex: 1; /* Take up 1/3 of the space */
  margin-bottom: 0;
  max-height: none;
  display: flex;
  flex-direction: column;
}

.modal-content.fullscreen .chart-code {
  flex: 1;
  overflow-y: auto;
  max-height: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .gallery-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    min-width: auto;
  }

  .filter-container {
    justify-content: space-between;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95vw;
    max-height: 95vh;
  }

  .code-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .code-actions {
    justify-content: center;
  }

  .toast {
    right: 10px;
    left: 10px;
    width: auto;
  }

  /* Fullscreen mobile layout */
  .modal-content.fullscreen .modal-body {
    flex-direction: column; /* Stack vertically on mobile */
    gap: 0.5rem;
  }

  .modal-content.fullscreen .chart-preview-container {
    flex: 2; /* Chart takes more space on mobile */
  }

  .modal-content.fullscreen .chart-code-container {
    flex: 1; /* Code takes less space on mobile */
    min-height: 200px; /* Ensure minimum height for code */
  }
}

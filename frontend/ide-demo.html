<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mermantic IDE Layout Demo</title>
  <link rel="stylesheet" href="public/css/styles.css">
  <style>
    /* Demo-specific styles */
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: var(--background-color);
      color: var(--text-color);
    }
    
    .demo-container {
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .demo-header {
      background: var(--gradient-surface);
      padding: 1rem;
      border-bottom: 1px solid var(--border-color);
      text-align: center;
    }
    
    .demo-header h1 {
      margin: 0;
      color: var(--primary-color);
    }
    
    .demo-header p {
      margin: 0.5rem 0 0 0;
      color: var(--text-muted);
    }
    
    /* Override chart-editor to show by default in fullscreen */
    .chart-editor.ide-layout {
      display: flex !important;
      flex: 1;
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: var(--background-color);
      z-index: 9999;
      padding: 0;
      overflow: hidden;
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <div class="demo-header">
      <h1>🎨 Mermantic IDE Layout Demo</h1>
      <p>New IDE-like interface for creating and editing Mermaid diagrams</p>
    </div>
    
    <section id="chart-editor" class="chart-editor ide-layout">
      <form id="chart-form">
        <input type="hidden" id="chart-id">
        
        <!-- IDE Toolbar -->
        <div class="ide-toolbar">
          <div class="toolbar-left">
            <h3 id="editor-title">Create New Diagram</h3>
          </div>
          <div class="toolbar-center">
            <div class="toolbar-actions">
              <button type="button" id="toggle-properties" class="toolbar-btn" title="Toggle Properties Panel">
                <span class="icon">⚙️</span> Properties
              </button>
              <button type="button" id="toggle-help" class="toolbar-btn" title="Show/Hide Help">
                <span class="icon">❓</span> Help
              </button>
            </div>
          </div>
          <div class="toolbar-right">
            <div class="toolbar-actions">
              <button type="submit" class="nui-button primary small">💾 Save</button>
              <button type="button" id="toggle-fullscreen" class="nui-button secondary small">⛶ Exit Fullscreen</button>
              <button type="button" id="cancel-edit" class="nui-button secondary small">✕ Cancel</button>
            </div>
          </div>
        </div>

        <!-- IDE Main Content -->
        <div class="ide-content">
          <!-- Properties Panel -->
          <div id="properties-panel" class="properties-panel">
            <div class="properties-header">
              <h4>Properties</h4>
              <button type="button" id="close-properties" class="close-btn" title="Close Properties Panel">✕</button>
            </div>
            <div class="properties-content">
              <div class="form-group">
                <label for="chart-title">Title</label>
                <input type="text" id="chart-title" name="chart-title" class="nui-input" value="My Awesome Diagram" required>
              </div>

              <div class="form-group">
                <label for="chart-folder">Folder</label>
                <div class="folder-input-container">
                  <input type="text" id="chart-folder" name="chart-folder" class="nui-input" placeholder="Enter folder name or leave empty" value="Projects">
                  <select id="folder-suggestions" class="folder-suggestions nui-select">
                    <option value="">Select existing folder...</option>
                    <option value="Projects">Projects</option>
                    <option value="Documentation">Documentation</option>
                  </select>
                </div>
              </div>

              <div class="form-group">
                <label for="chart-notes">Notes</label>
                <textarea id="chart-notes" name="chart-notes" class="nui-textarea" placeholder="Add notes or description for this diagram..." rows="3">This is a sample diagram showing the new IDE layout.</textarea>
              </div>

              <div class="form-group">
                <label for="chart-theme">Diagram Theme</label>
                <select id="chart-theme" name="chart-theme" class="nui-select">
                  <option value="default">Default</option>
                  <option value="dark" selected>Dark</option>
                  <option value="forest">Forest</option>
                  <option value="base">Base</option>
                  <option value="neutral">Neutral</option>
                </select>
                <small class="form-help">Choose the visual theme for your diagram</small>
              </div>

              <div class="form-group">
                <label for="chart-public">
                  <input type="checkbox" id="chart-public" name="chart-public">
                  Make diagram public
                </label>
              </div>
            </div>
          </div>

          <!-- Editor Container -->
          <div class="editor-container">
            <div class="editor-panel">
              <div class="editor-header">
                <h4>Mermaid Syntax <span id="syntax-indicator" class="syntax-indicator">✓</span></h4>
              </div>
              <textarea id="chart-content" class="mermaid-editor" required>graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    D --> B
    C --> E[Deploy]
    E --> F[Success!]</textarea>

              <div id="help-panel" class="help-panel" style="display: none;">
                <h5>Quick Reference</h5>
                <div class="help-examples">
                  <button type="button" class="help-example" data-example="flowchart">Flowchart</button>
                  <button type="button" class="help-example" data-example="sequence">Sequence</button>
                  <button type="button" class="help-example" data-example="gantt">Gantt</button>
                  <button type="button" class="help-example" data-example="pie">Pie Chart</button>
                </div>
                <div class="help-shortcuts">
                  <strong>Shortcuts:</strong><br>
                  <kbd>Ctrl+S</kbd> Save • <kbd>Esc</kbd> Cancel • <kbd>Ctrl+Enter</kbd> Update Preview • <kbd>F11</kbd> Fullscreen
                </div>
                <div class="help-unicode">
                  <strong>Unicode Characters:</strong><br>
                  Some Unicode characters (emojis, special symbols) may be automatically replaced with compatible alternatives for better rendering.
                </div>
              </div>
            </div>
            
            <div class="preview-panel">
              <div class="preview-header">
                <h4>Preview</h4>
                <div class="preview-controls">
                  <div class="preview-zoom-controls" id="preview-zoom-controls" style="display: flex;">
                    <button type="button" class="zoom-btn" id="preview-zoom-out" title="Zoom Out">−</button>
                    <button type="button" class="zoom-btn" id="preview-zoom-reset" title="Reset Zoom">⌂</button>
                    <button type="button" class="zoom-btn" id="preview-zoom-in" title="Zoom In">+</button>
                  </div>
                  <div class="preview-download-group">
                    <button type="button" id="preview-download-png" class="nui-button small download-btn" title="Download as PNG">📥 PNG</button>
                    <button type="button" id="preview-download-svg" class="nui-button small download-btn" title="Download as SVG">📥 SVG</button>
                  </div>
                </div>
              </div>
              <div id="chart-preview" class="mermaid-preview chart-zoomable">
                <div style="padding: 2rem; text-align: center; color: var(--text-muted);">
                  <p>📊 Mermaid diagram preview would appear here</p>
                  <p>In the actual application, this would show the rendered diagram</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </section>
  </div>

  <script>
    // Demo functionality
    document.getElementById('toggle-properties').addEventListener('click', function() {
      const propertiesPanel = document.getElementById('properties-panel');
      propertiesPanel.classList.toggle('hidden');
    });

    document.getElementById('close-properties').addEventListener('click', function() {
      const propertiesPanel = document.getElementById('properties-panel');
      propertiesPanel.classList.add('hidden');
    });

    document.getElementById('toggle-help').addEventListener('click', function() {
      const helpPanel = document.getElementById('help-panel');
      helpPanel.style.display = helpPanel.style.display === 'none' ? 'block' : 'none';
    });

    document.getElementById('toggle-fullscreen').addEventListener('click', function() {
      const editor = document.getElementById('chart-editor');
      const button = this;

      if (editor.classList.contains('fullscreen')) {
        editor.classList.remove('fullscreen');
        button.textContent = '⛶ Fullscreen';
        document.body.style.overflow = '';
      } else {
        editor.classList.add('fullscreen');
        button.textContent = '⛶ Exit Fullscreen';
        document.body.style.overflow = 'hidden';
      }
    });

    // Prevent form submission for demo
    document.getElementById('chart-form').addEventListener('submit', function(e) {
      e.preventDefault();
      alert('This is a demo - form submission prevented');
    });

    document.getElementById('cancel-edit').addEventListener('click', function() {
      alert('This is a demo - cancel functionality would close the editor');
    });

    // Zoom controls with proper event handling
    document.getElementById('preview-zoom-in').addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      alert('Zoom In functionality - would zoom the preview');
    });

    document.getElementById('preview-zoom-out').addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      alert('Zoom Out functionality - would zoom the preview');
    });

    document.getElementById('preview-zoom-reset').addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      alert('Reset Zoom functionality - would reset preview zoom');
    });

    // Download controls with proper event handling
    document.getElementById('preview-download-png').addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      alert('Download PNG functionality - would download the diagram as PNG');
    });

    document.getElementById('preview-download-svg').addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      alert('Download SVG functionality - would download the diagram as SVG');
    });
  </script>
</body>
</html>

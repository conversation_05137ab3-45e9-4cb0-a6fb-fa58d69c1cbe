/**
 * Monaco Editor Integration Utilities
 * Provides drop-in replacement functions for existing textarea-based Mermaid editors
 */

class MonacoIntegration {
  static editors = new Map();
  static isMonacoLoaded = false;

  /**
   * Replace a textarea with Monaco Editor
   * @param {string} textareaId - ID of the textarea to replace
   * @param {Object} options - Monaco editor options
   * @returns {Promise<MonacoMermaidEditor>} - The created editor instance
   */
  static async replaceTextarea(textareaId, options = {}) {
    const textarea = document.getElementById(textareaId);
    if (!textarea) {
      console.error(`Textarea with id '${textareaId}' not found`);
      return null;
    }

    // Get existing content and properties
    const existingContent = textarea.value;
    const existingClasses = textarea.className;
    const parentElement = textarea.parentElement;

    // Create container for Monaco Editor
    const editorContainer = document.createElement('div');
    editorContainer.id = textareaId + '-monaco';
    editorContainer.className = existingClasses.replace('mermaid-editor', 'monaco-mermaid-editor');
    editorContainer.style.height = textarea.style.height || '100%';
    editorContainer.style.width = textarea.style.width || '100%';

    // Replace textarea with editor container
    parentElement.replaceChild(editorContainer, textarea);

    // Create Monaco Editor
    const editor = new MonacoMermaidEditor(editorContainer.id, {
      value: existingContent,
      ...options
    });

    // Store editor reference
    this.editors.set(textareaId, editor);

    return editor;
  }

  /**
   * Initialize Monaco Editor for demo section (index.html)
   */
  static async initializeDemoEditor() {
    try {
      // Replace the demo editor textarea
      const demoEditor = await this.replaceTextarea('demo-editor', {
        value: `journey
    title User Journey for New Signup
    section Sign Up
      Visit Site: 5: User
      Click Sign Up: 4: User
      Fill Form: 3: User`
      });

      if (!demoEditor) return;

      // Connect to existing preview functionality
      const demoPreview = document.getElementById('demo-preview');
      if (demoPreview) {
        demoEditor.setOnContentChange((content) => {
          this.updateMermaidPreview(demoPreview, content);
        });

        // Initial preview update
        this.updateMermaidPreview(demoPreview, demoEditor.getValue());
      }

      return demoEditor;
    } catch (error) {
      console.error('Failed to initialize demo editor:', error);
      return null;
    }
  }

  /**
   * Initialize Monaco Editor for dashboard/chart editor
   */
  static async initializeChartEditor() {
    try {
      // Replace the chart content textarea with empty content
      // The placeholder will guide users on what to enter
      const chartEditor = await this.replaceTextarea('chart-content', {
        value: ''
      });

      if (!chartEditor) return;

      // Connect to existing preview functionality
      const chartPreview = document.getElementById('chart-preview');
      if (chartPreview) {
        chartEditor.setOnContentChange((content) => {
          // Use existing updateChartPreview function if available
          if (typeof updateChartPreview === 'function') {
            updateChartPreview();
          } else {
            this.updateMermaidPreview(chartPreview, content);
          }
        });
      }

      return chartEditor;
    } catch (error) {
      console.error('Failed to initialize chart editor:', error);
      return null;
    }
  }

  /**
   * Generic Mermaid preview update function
   * @param {HTMLElement} previewContainer - The preview container element
   * @param {string} content - The Mermaid content to render
   */
  static updateMermaidPreview(previewContainer, content) {
    if (!previewContainer || !content.trim()) {
      if (previewContainer) {
        previewContainer.innerHTML = '';
      }
      return;
    }

    try {
      // Clear the preview div
      previewContainer.innerHTML = '';

      // Generate a unique ID for the diagram
      const id = 'mermaid-diagram-' + Date.now();

      // Create a div element with the unique ID
      const mermaidDiv = document.createElement('div');
      mermaidDiv.id = id;
      mermaidDiv.className = 'mermaid';

      // Set the diagram code BEFORE appending to DOM
      mermaidDiv.textContent = content;

      // Append the div element to the preview div
      previewContainer.appendChild(mermaidDiv);

      // Use requestAnimationFrame to ensure DOM is ready
      requestAnimationFrame(() => {
        try {
          if (window.mermaid && mermaid.init) {
            mermaid.init(undefined, mermaidDiv);
          }
        } catch (renderError) {
          console.error('Error rendering Mermaid diagram:', renderError);
          previewContainer.innerHTML = `<div class="error-message">
            <strong>Error rendering diagram:</strong><br>
            ${renderError.message}<br><br>
            <strong>Common issues to check:</strong><br>
            - Indentation and whitespace<br>
            - Special characters (try using simple ASCII characters)<br>
            - Missing connections or closing brackets<br>
            - Subgraph syntax (ensure proper nesting)<br>
          </div>`;
        }
      });

    } catch (error) {
      console.error('Error setting up Mermaid diagram:', error);
      previewContainer.innerHTML = `<div class="error-message">
        <strong>Error setting up diagram:</strong><br>
        ${error.message}
      </div>`;
    }
  }

  /**
   * Get editor instance by original textarea ID
   * @param {string} textareaId - Original textarea ID
   * @returns {MonacoMermaidEditor|null} - Editor instance or null
   */
  static getEditor(textareaId) {
    return this.editors.get(textareaId) || null;
  }

  /**
   * Dispose of an editor instance
   * @param {string} textareaId - Original textarea ID
   */
  static disposeEditor(textareaId) {
    const editor = this.editors.get(textareaId);
    if (editor) {
      editor.dispose();
      this.editors.delete(textareaId);
    }
  }

  /**
   * Dispose of all editor instances
   */
  static disposeAllEditors() {
    for (const [id, editor] of this.editors) {
      editor.dispose();
    }
    this.editors.clear();
  }

  /**
   * Check if Monaco Editor is supported in current browser
   * @returns {boolean} - True if supported
   */
  static isSupported() {
    return MonacoMermaidEditor.isSupported();
  }

  /**
   * Initialize Monaco Editor based on current page
   */
  static async autoInitialize() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      return new Promise(resolve => {
        document.addEventListener('DOMContentLoaded', () => {
          this.autoInitialize().then(resolve);
        });
      });
    }

    // Check if Monaco is supported
    if (!this.isSupported()) {
      console.warn('Monaco Editor not supported in this browser, keeping textarea');
      return;
    }

    // Initialize based on page content
    const demoEditor = document.getElementById('demo-editor');
    const chartEditor = document.getElementById('chart-content');

    if (demoEditor) {
      await this.initializeDemoEditor();
    }

    if (chartEditor) {
      await this.initializeChartEditor();
    }
  }

  /**
   * Add copy button to existing editor header
   * @param {string} editorId - Editor ID
   * @param {string} headerSelector - CSS selector for header element
   */
  static addCopyButton(editorId, headerSelector) {
    const editor = this.getEditor(editorId);
    const header = document.querySelector(headerSelector);

    if (!editor || !header) {
      console.warn('Editor or header not found for copy button');
      return;
    }

    // Create copy button
    const copyButton = document.createElement('button');
    copyButton.innerHTML = '📋 Copy';
    copyButton.className = 'copy-button';
    copyButton.style.padding = '5px 10px';
    copyButton.style.backgroundColor = '#007acc';
    copyButton.style.color = 'white';
    copyButton.style.border = 'none';
    copyButton.style.borderRadius = '3px';
    copyButton.style.cursor = 'pointer';
    copyButton.style.fontSize = '12px';
    copyButton.style.marginLeft = '10px';

    // Add to header
    header.appendChild(copyButton);

    // Setup copy functionality
    copyButton.addEventListener('click', async () => {
      try {
        await navigator.clipboard.writeText(editor.getValue());
        copyButton.innerHTML = '✓ Copied!';
        copyButton.style.backgroundColor = '#28a745';
        setTimeout(() => {
          copyButton.innerHTML = '📋 Copy';
          copyButton.style.backgroundColor = '#007acc';
        }, 2000);
      } catch (error) {
        console.error('Failed to copy to clipboard:', error);
        copyButton.innerHTML = '❌ Failed';
        copyButton.style.backgroundColor = '#dc3545';
        setTimeout(() => {
          copyButton.innerHTML = '📋 Copy';
          copyButton.style.backgroundColor = '#007acc';
        }, 2000);
      }
    });
  }
}

// Auto-initialize when script loads
if (typeof window !== 'undefined') {
  window.MonacoIntegration = MonacoIntegration;

  // Auto-initialize if not in a module context
  if (!window.module) {
    MonacoIntegration.autoInitialize();
  }
}

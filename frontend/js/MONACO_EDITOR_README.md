# Monaco Editor Integration for Mermantic

This document describes the Monaco Editor integration that replaces the textarea-based Mermaid editors with a full-featured code editor.

## Features

- **Syntax Highlighting**: Custom Mermaid language mode with highlighting for:
  - Diagram types (graph, sequenceDiagram, classDiagram, etc.)
  - Direction keywords (TD, TB, BT, RL, LR)
  - Arrows and connections (-->, ->, ==>, etc.)
  - Node shapes and labels
  - Comments and strings
  
- **Dark Theme**: Custom dark theme that matches the site's aesthetic
- **Live Preview**: Real-time Mermaid diagram rendering as you type
- **Copy to Clipboard**: Built-in copy functionality
- **localStorage Persistence**: Automatically saves and restores editor content
- **Graceful Fallback**: Falls back to textarea if Monaco Editor fails to load

## Files

### Core Files
- `monaco-mermaid-editor.js` - Main Monaco Editor class
- `monaco-integration.js` - Integration utilities and drop-in replacement functions

### Updated Pages
- `index.html` - Demo editor with Monaco integration
- `dashboard.html` - Chart editor with Monaco integration
- `monaco-test.html` - Test page for validation

## Usage

### Basic Usage

```javascript
// Create a new Monaco Editor instance
const editor = new MonacoMermaidEditor('container-id', {
  value: 'graph TD\n  A --> B',
  theme: 'vs-dark'
});

// Set up content change callback
editor.setOnContentChange((content) => {
  // Update preview
  updateMermaidPreview(content);
});
```

### Drop-in Replacement

```javascript
// Replace existing textarea with Monaco Editor
MonacoIntegration.replaceTextarea('textarea-id').then(editor => {
  // Editor is ready
  console.log('Monaco Editor initialized');
});

// Auto-initialize based on page content
MonacoIntegration.autoInitialize();
```

### With Copy Button

```javascript
// Create editor with integrated copy button
const editor = MonacoMermaidEditor.createWithCopyButton('container-id', {
  value: 'graph TD\n  A --> B'
});
```

## API Reference

### MonacoMermaidEditor Class

#### Constructor
```javascript
new MonacoMermaidEditor(containerId, options)
```

#### Methods
- `getValue()` - Get current editor content
- `setValue(value)` - Set editor content
- `setOnContentChange(callback)` - Set content change callback
- `focus()` - Focus the editor
- `layout()` - Trigger editor layout recalculation
- `dispose()` - Clean up editor instance
- `persistContent()` - Save content to localStorage
- `loadPersistedContent()` - Load content from localStorage
- `clearPersistedContent()` - Clear saved content

#### Static Methods
- `MonacoMermaidEditor.isSupported()` - Check browser support
- `MonacoMermaidEditor.createWithCopyButton(containerId, options)` - Create with copy button

### MonacoIntegration Class

#### Static Methods
- `replaceTextarea(textareaId, options)` - Replace textarea with Monaco Editor
- `initializeDemoEditor()` - Initialize demo editor (index.html)
- `initializeChartEditor()` - Initialize chart editor (dashboard.html)
- `updateMermaidPreview(container, content)` - Update Mermaid preview
- `getEditor(textareaId)` - Get editor instance by original textarea ID
- `disposeEditor(textareaId)` - Dispose editor instance
- `addCopyButton(editorId, headerSelector)` - Add copy button to header
- `autoInitialize()` - Auto-initialize based on page content

## Configuration Options

```javascript
{
  theme: 'vs-dark',              // Editor theme
  language: 'mermaid',           // Language mode
  automaticLayout: true,         // Auto-resize
  minimap: { enabled: false },   // Disable minimap
  scrollBeyondLastLine: false,   // Scroll behavior
  wordWrap: 'on',               // Word wrapping
  lineNumbers: 'on',            // Line numbers
  fontSize: 14,                 // Font size
  fontFamily: 'Fira Code, Consolas, monospace',
  tabSize: 2,                   // Tab size
  insertSpaces: true,           // Use spaces for tabs
  renderWhitespace: 'selection', // Whitespace rendering
  folding: true,                // Code folding
  showFoldingControls: 'always' // Folding controls
}
```

## Browser Support

Monaco Editor requires modern browsers with ES6 support. The integration automatically falls back to textarea for unsupported browsers.

Supported browsers:
- Chrome 63+
- Firefox 78+
- Safari 13+
- Edge 79+

## Integration Notes

### Existing Code Compatibility

The integration maintains compatibility with existing code by:
- Preserving the same container IDs
- Providing fallback to textarea functionality
- Maintaining the same event handling patterns
- Using the same CSS classes where possible

### Performance

- Monaco Editor loads asynchronously from CDN
- Syntax highlighting is optimized for Mermaid syntax
- Debounced preview updates (300ms default)
- Automatic layout recalculation on window resize

### Error Handling

- Graceful fallback to textarea if Monaco fails to load
- Console warnings for initialization failures
- Error messages in preview for invalid Mermaid syntax
- Retry logic for preview rendering

## Troubleshooting

### Monaco Editor Not Loading
- Check browser console for CDN loading errors
- Verify internet connection
- Check if browser supports Monaco Editor

### Syntax Highlighting Not Working
- Ensure Mermaid language mode is registered
- Check for JavaScript errors in console
- Verify Monaco Editor initialization completed

### Preview Not Updating
- Check if Mermaid.js is loaded
- Verify content change callback is set
- Check for errors in preview update function

### Persistence Not Working
- Check if localStorage is available
- Verify storage quota not exceeded
- Check for privacy/incognito mode restrictions
